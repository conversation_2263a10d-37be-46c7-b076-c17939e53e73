<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改跟踪止盈')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-trailing-edit" th:object="${trailingProfit}">
            <h4 class="form-header h4">跟踪止盈信息</h4>
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">用户名称：</label>
                    <div class="col-sm-8">
                        <input name="accountName" th:field="*{accountName}" readonly="true"  class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">平台：</label>
                    <div class="col-sm-8">
                        <input name="platform" th:field="*{platform}" readonly="true"  class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">币对：</label>
                    <div class="col-sm-8">
                        <input name="symbol" th:field="*{symbol}" readonly="true"  class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">建仓价：</label>
                    <div class="col-sm-8">
                        <input name="openPrice" th:field="*{openPrice}" readonly="true"  class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">止损价：</label>
                    <div class="col-sm-8">
                        <input name="stopLossPrice" th:field="*{stopLossPrice}" readonly="true"  class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">交易数量：</label>
                    <div class="col-sm-8">
                        <input name="amount" th:field="*{amount}" readonly="true"  class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">杠杆倍数：</label>
                    <div class="col-sm-8">
                        <input name="leverage" th:field="*{leverage}" readonly="true" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">订单方向：</label>
                    <div class="col-sm-8">
                        <input name="positionSide" th:field="*{positionSide}" readonly="true"  class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">交易模式：</label>
                    <div class="col-sm-8">
                        <input name="tradeMode" th:field="*{tradeMode}" readonly="true"  class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">状态</label>
                    <div class="col-sm-8">
                        <input name="state" th:field="*{state}" readonly="true"  class="form-control" type="text">
                    </div>
                </div>
            </div>
            <h4 class="form-header h4">分阶段止盈信息</h4>
            <div class="row">
                <div class="col-sm-12">
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "strategy/trailing";
        $("#form-trailing-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-trailing-edit').serialize());
            }
        }

        $(function() {
            var options = {
                data: [[${trailingProfit.trailingDetailList}]],
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                {
                    field: 'priceGain',
                    align: 'center',
                    title: '触发止盈/损'
                },
                {
                    field: 'triggerPrice',
                    align: 'center',
                    title: '触发止盈/损点位'
                },
                {
                    field: 'takeProfit',
                    align: 'center',
                    title: '止盈/损点'
                },
                {
                    field: 'state',
                    align: 'center',
                    title: '状态',
                    formatter: function(value, row, index) {
                        var display = '--';
                        if(value === 0) {
                            display = '未启用';
                        } else if(value == 1) {
                            display = '启用中';
                        }
                        return display;
                    }
                },
                    {
                        field: 'type',
                        align: 'center',
                        title: '类型',
                        formatter: function(value, row, index) {
                            var display = '--';
                            if(value === 0) {
                                display = '分段止盈';
                            } else if(value == 1) {
                                display = '分段止损';
                            }
                            return display;
                        }
                    }
                ]
            };
            $.table.init(options);
        });

    </script>
</body>
</html>