<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('列表')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>账户名称：</label>
                            <input type="text" name="accountName"/>
                        </li>
                        <li>
                            <label>平台：</label>
                            <input type="text" name="platform"/>
                        </li>
                        <li>
                            <label>状态：</label>
                            <input type="text" name="state"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="strategy:account:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="strategy:account:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="strategy:account:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-primary multiple disabled" onclick="assetClick()" shiro:hasPermission="strategy:account:asset">
                <i class="fa fa-plus"></i> 余额
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('strategy:account:edit')}]];
    var removeFlag = [[${@permission.hasPermi('strategy:account:remove')}]];
    var prefix = ctx + "strategy/account";

    function assetClick() {
        var row = $("#" + table.options.id).bootstrapTable('getSelections')[0];
        var url = table.options.assetUrl.replace("{id}", row.id);
        $.modal.open("资产" + table.options.modalName, url);
    }

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            assetUrl: prefix + "/asset/{id}",
            removeUrl: prefix + "/remove",
            modalName: "[]",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: '${comment}',
                    visible: false
                },
                {
                    field: 'accountName',
                    title: 'account_name'
                },
                {
                    field: 'apikey',
                    title: 'apiKey:'
                },
                {
                    field: 'platform',
                    title: '平台'
                },
                {
                    field: 'state',
                    title: '状态'
                },
                {
                    field: 'message',
                    title: '描述信息'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>