<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.strategy.mapper.TrailingProfitMapper">
    
    <resultMap type="TrailingProfit" id="TrailingProfitResult">
        <result property="id"    column="id"    />
        <result property="groupId"    column="group_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="accountName"    column="account_name"    />
        <result property="platform"    column="platform"    />
        <result property="symbol"    column="symbol"    />
        <result property="openPrice"    column="open_price"    />
        <result property="marketPrice"    column="market_price"    />
        <result property="closePrice"    column="close_price"    />
        <result property="stopLossPrice"    column="stop_loss_price"    />
        <result property="stopLossRate"    column="stop_loss_rate"    />
        <result property="highestPrice"    column="highest_price"    />
        <result property="lowestPrice"    column="lowest_price"    />
        <result property="amount"    column="amount"    />
        <result property="profitValue"    column="profit_value"    />
        <result property="profitRate"    column="profit_rate"    />
        <result property="instType"    column="inst_type"    />
        <result property="leverage"    column="leverage"    />
        <result property="positionSide"    column="position_side"    />
        <result property="orderType"    column="order_type"    />
        <result property="tradeMode"    column="trade_mode"    />
        <result property="fee"    column="fee"    />
        <result property="refOrderOpen"    column="ref_order_open"    />
        <result property="refOrderClose"    column="ref_order_close"    />
        <result property="state"    column="state"    />
        <result property="strategyType"    column="strategy_type"    />
        <result property="followContent"    column="follow_content"    />
        <result property="fresh"    column="fresh"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="message"    column="message"    />
    </resultMap>

    <resultMap id="TrailingProfitTrailingDetailResult" type="TrailingProfit" extends="TrailingProfitResult">
        <collection property="trailingDetailList" ofType="TrailingDetail" column="id" select="selectTrailingDetailList" />
    </resultMap>

    <resultMap type="TrailingDetail" id="TrailingDetailResult">
        <result property="id"    column="id"    />
        <result property="trailingProfitId"    column="trailing_profit_id"    />
        <result property="priceGain"    column="price_gain"    />
        <result property="triggerPrice"    column="trigger_price"    />
        <result property="takeProfit"    column="take_profit"    />
        <result property="state"    column="state"    />
        <result property="type"    column="type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="message"    column="message"    />
    </resultMap>

    <sql id="selectTrailingProfitVo">
        select id, group_id,  user_id, user_name, account_name, platform, symbol, open_price,market_price,close_price,
        stop_loss_price, stop_loss_rate, highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
        leverage, position_side, order_type, trade_mode, fee, ref_order_open, ref_order_close,
        state, strategy_type, follow_content,fresh,create_time, update_time, message from trailing_profit
    </sql>

    <select id="selectTrailingProfitList" parameterType="TrailingProfit" resultMap="TrailingProfitResult">
        <include refid="selectTrailingProfitVo"/>
        <where>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="symbol != null  and symbol != ''"> and symbol = #{symbol}</if>
            <if test="openPrice != null "> and open_price = #{openPrice}</if>
            <if test="marketPrice != null "> and market_price = #{marketPrice}</if>
            <if test="closePrice != null "> and close_price = #{closePrice}</if>
            <if test="stopLossPrice != null "> and stop_loss_price = #{stopLossPrice}</if>
            <if test="stopLossRate != null "> and stop_loss_rate = #{stopLossRate}</if>
            <if test="highestPrice != null "> and highest_price = #{highestPrice}</if>
            <if test="lowestPrice != null "> and lowest_price = #{lowestPrice}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="profitValue != null "> and profit_value = #{profitValue}</if>
            <if test="profitRate != null "> and profit_rate = #{profitRate}</if>
            <if test="instType != null  and instType != ''"> and inst_type = #{instType}</if>
            <if test="leverage != null "> and leverage = #{leverage}</if>
            <if test="positionSide != null  and positionSide != ''"> and position_side = #{positionSide}</if>
            <if test="orderType != null  and orderType != ''"> and order_type = #{orderType}</if>
            <if test="tradeMode != null  and tradeMode != ''"> and trade_mode = #{tradeMode}</if>
            <if test="fee != null "> and fee = #{fee}</if>
            <if test="refOrderOpen != null  and refOrderOpen != ''"> and ref_order_open = #{refOrderOpen}</if>
            <if test="refOrderClose != null  and refOrderClose != ''"> and ref_order_close = #{refOrderClose}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="strategyType != null "> and strategy_type = #{strategyType}</if>
            <if test="followContent != null "> and follow_content = #{followContent}</if>
            <if test="fresh != null "> and fresh = #{fresh}</if>
            <if test="message != null  and message != ''"> and message = #{message}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectTrailingProfitById" parameterType="Long" resultMap="TrailingProfitTrailingDetailResult">
        select id, group_id, user_id, user_name, account_name, platform, symbol, open_price, market_price,close_price,
        stop_loss_price, stop_loss_rate, highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
        leverage, position_side, order_type, trade_mode, fee, ref_order_open,ref_order_close,
        state, strategy_type, follow_content,ref_order_close, fresh, create_time, update_time, message
        from trailing_profit
        where id = #{id}
    </select>

    <select id="selectTrailingFollowByGroup" parameterType="Long" resultMap="TrailingProfitTrailingDetailResult">
        select id, group_id, user_id, user_name, account_name, platform, symbol, open_price, market_price,close_price,
        stop_loss_price, stop_loss_rate, highest_price, lowest_price, amount, profit_value, profit_rate, inst_type,
        leverage, position_side, order_type, trade_mode, fee, ref_order_open,ref_order_close,
        state, strategy_type, follow_content,ref_order_close, fresh, create_time, update_time, message
        from trailing_profit
        where group_id = #{groupId} and strategy_type = 1 and state = 0
        limit 1
    </select>

    <select id="selectByIds" parameterType="String" resultMap="TrailingProfitResult">
        SELECT id, group_id, user_id, user_name, account_name, platform, symbol, open_price, market_price,close_price,
                stop_loss_price, stop_loss_rate, highest_price,lowest_price, amount, profit_value, profit_rate, inst_type,
                leverage, position_side, order_type, trade_mode, fee, ref_order_open,
                state, strategy_type,follow_content,ref_order_close, state, fresh, create_time, update_time, message
        FROM trailing_profit
        WHERE id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByRefresh" parameterType="Integer" resultMap="TrailingProfitResult">
        SELECT id, group_id, user_id, user_name, account_name, platform, symbol, open_price, market_price,close_price,
        stop_loss_price, stop_loss_rate, highest_price,lowest_price, amount, profit_value, profit_rate, inst_type,
        leverage, position_side, order_type, trade_mode, fee, ref_order_open,ref_order_close, state,
        strategy_type,follow_content,ref_order_close,fresh, create_time, update_time, message
        FROM trailing_profit
        WHERE fresh=0 and state in
        <foreach item="state" collection="stateList" open="(" separator="," close=")">
            #{state}
        </foreach>
    </select>

    <select id="selectTrailingDetailList" resultMap="TrailingDetailResult">
        select id, trailing_profit_id,  price_gain, trigger_price,  take_profit, state,type, create_time, update_time, message
        from trailing_detail
        where trailing_profit_id = #{trailing_profit_id}
    </select>

    <select id="selectByGroup" parameterType="Long" resultMap="TrailingProfitResult">
        select id, group_id, user_id, user_name, account_name, platform, symbol, open_price,market_price,close_price,
        stop_loss_price, stop_loss_rate, highest_price, lowest_price,amount, profit_value, profit_rate, inst_type,
        leverage, position_side, order_type, trade_mode, fee, ref_order_open, ref_order_close,
        state, strategy_type, follow_content, ref_order_close,fresh, create_time, update_time, message
        from trailing_profit
        where user_id = #{userId} and group_id = #{groupId}
    </select>

    <select id="selectLastestGroupId" parameterType="map" resultType="long">
        SELECT group_id FROM trailing_profit
        WHERE user_id=#{userId}
        ORDER BY group_id DESC
        LIMIT 1
    </select>


    <insert id="insertTrailingProfit" parameterType="TrailingProfit" useGeneratedKeys="true" keyProperty="id">
        insert into trailing_profit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupId != null">group_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="accountName != null">account_name,</if>
            <if test="platform != null">platform,</if>
            <if test="symbol != null">symbol,</if>
            <if test="openPrice != null">open_price,</if>
            <if test="marketPrice != null">market_price,</if>
            <if test="closePrice != null">close_price,</if>
            <if test="stopLossPrice != null">stop_loss_price,</if>
            <if test="stopLossRate != null">stop_loss_rate,</if>
            <if test="highestPrice != null">highest_price,</if>
            <if test="lowestPrice != null">lowest_price,</if>
            <if test="amount != null">amount,</if>
            <if test="profitValue != null">profit_value,</if>
            <if test="profitRate != null">profit_rate,</if>
            <if test="instType != null">inst_type,</if>
            <if test="leverage != null">leverage,</if>
            <if test="positionSide != null">position_side,</if>
            <if test="orderType != null">order_type,</if>
            <if test="tradeMode != null">trade_mode,</if>
            <if test="fee != null">fee,</if>
            <if test="refOrderOpen != null and refOrderOpen != ''">ref_order_open,</if>
            <if test="refOrderClose != null and refOrderClose != ''">ref_order_close,</if>
            <if test="state != null">state,</if>
            <if test="strategyType != null">strategy_type,</if>
            <if test="followContent != null">follow_content,</if>
            <if test="fresh != null">fresh,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="message != null and message != ''">message,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupId != null">#{groupId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="accountName != null">#{accountName},</if>
            <if test="platform != null">#{platform},</if>
            <if test="symbol != null">#{symbol},</if>
            <if test="openPrice != null">#{openPrice},</if>
            <if test="marketPrice != null">#{marketPrice},</if>
            <if test="closePrice != null">#{closePrice},</if>
            <if test="stopLossPrice != null">#{stopLossPrice},</if>
            <if test="stopLossRate != null">#{stopLossRate},</if>
            <if test="highestPrice != null">#{highestPrice},</if>
            <if test="lowestPrice != null">#{lowestPrice},</if>
            <if test="amount != null">#{amount},</if>
            <if test="profitValue != null">#{profitValue},</if>
            <if test="profitRate != null">#{profitRate},</if>
            <if test="instType != null">#{instType},</if>
            <if test="leverage != null">#{leverage},</if>
            <if test="positionSide != null">#{positionSide},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="tradeMode != null">#{tradeMode},</if>
            <if test="fee != null">#{fee},</if>
            <if test="refOrderOpen != null and refOrderOpen != ''">#{refOrderOpen},</if>
            <if test="refOrderClose != null and refOrderClose != ''">#{refOrderClose},</if>
            <if test="state != null">#{state},</if>
            <if test="strategyType != null">#{strategyType},</if>
            <if test="followContent != null">#{followContent},</if>
            <if test="fresh != null">#{fresh},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="message != null and message != ''">#{message},</if>
         </trim>
    </insert>

    <update id="updateTrailingProfit" parameterType="TrailingProfit">
        update trailing_profit
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="accountName != null">account_name = #{accountName},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="symbol != null">symbol = #{symbol},</if>
            <if test="openPrice != null">open_price = #{openPrice},</if>
            <if test="marketPrice != null">market_price = #{marketPrice},</if>
            <if test="closePrice != null">close_price = #{closePrice},</if>
            <if test="stopLossPrice != null">stop_loss_price = #{stopLossPrice},</if>
            <if test="stopLossRate != null">stop_loss_rate = #{stopLossRate},</if>
            <if test="highestPrice != null">highest_price = #{highestPrice},</if>
            <if test="lowestPrice != null">lowest_price = #{lowestPrice},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="profitValue != null">profit_value = #{profitValue},</if>
            <if test="profitRate != null">profit_rate = #{profitRate},</if>
            <if test="instType != null">inst_type = #{instType},</if>
            <if test="leverage != null">leverage = #{leverage},</if>
            <if test="positionSide != null">position_side = #{positionSide},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="tradeMode != null">trade_mode = #{tradeMode},</if>
            <if test="fee != null">fee = #{fee},</if>
            <if test="refOrderOpen != null and refOrderOpen != ''">ref_order_open = #{refOrderOpen},</if>
            <if test="refOrderClose != null and refOrderClose != ''">ref_order_close = #{refOrderClose},</if>
            <if test="state != null">state = #{state},</if>
            <if test="strategyType != null">strategy_type = #{strategyType},</if>
            <if test="followContent != null">follow_content = #{followContent},</if>
            <if test="fresh != null">fresh = #{fresh},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="message != null and message != ''">message = #{message},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateTrailingDetail" parameterType="TrailingDetail">
        update trailing_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="priceGain != null">price_gain = #{priceGain},</if>
            <if test="triggerPrice != null">trigger_price = #{triggerPrice},</if>
            <if test="takeProfit != null">take_profit = #{takeProfit},</if>
            <if test="state != null">state = #{state},</if>
            <if test="type != null">type = #{type},</if>
            <if test="updateTime != null">update_time = now(),</if>
        </trim>
        where id = #{id}
    </update>

    <update id="addTrailingGroup">
        update trailing_profit
        SET group_id= #{groupId}
        where user_id= #{userId} and state = -1 and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="startGroup">
        update trailing_profit
        SET state = 0
        where user_id= #{userId} and group_id = #{groupId} and state = -1
    </update>

    <delete id="deleteTrailingProfitById" parameterType="Long">
        delete from trailing_profit where id = #{id}
    </delete>

    <delete id="deleteTrailingProfitByIds" parameterType="String">
        delete from trailing_profit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteTrailingDetailByTrailingProfitIds" parameterType="String">
        delete from trailing_detail where trailing_profit_id in 
        <foreach item="trailingProfitId" collection="array" open="(" separator="," close=")">
            #{trailingProfitId}
        </foreach>
    </delete>

    <delete id="deleteTrailingDetailByTrailingProfitId" parameterType="Long">
        delete from trailing_detail where trailing_profit_id = #{trailingProfitId}
    </delete>

    <insert id="batchTrailingDetail">
        insert into trailing_detail( id, trailing_profit_id, price_gain, trigger_price, take_profit, state,type, create_time, update_time, message) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.trailingProfitId}, #{item.priceGain},#{item.triggerPrice}, #{item.takeProfit}, #{item.state}, #{item.type}, #{item.createTime}, #{item.updateTime}, #{item.message})
        </foreach>
    </insert>

</mapper>