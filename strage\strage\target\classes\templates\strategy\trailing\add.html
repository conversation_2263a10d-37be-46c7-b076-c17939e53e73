<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增跟踪止盈')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-trailing-add">
            <h4 class="form-header h4">跟踪止盈信息</h4>
            <div class="col-xs-12">
                <div class="form-group">

                    <label class="col-sm-3 control-label">账户名称：</label>
                    <div class="col-sm-8">
                        <select id="exAccountId" name="accountName" onchange="handleAccount(this)" class="form-control">
                            <option  value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">当前账户余额：</label>
                    <div class="col-sm-8">
                        <label id="balanceId" class="col-sm-3 control-label"></label>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">平台：</label>
                    <div class="col-sm-8">
                        <select name="platform" class="form-control" width="200">
                            <option value="BINANCE">币安</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">币对：</label>
                    <div class="col-sm-8">
                        <select id="symbolId" name="symbol" class="form-control">
                            <option  value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">止损比例%：</label>
                    <div class="col-sm-8">
                        <input name="stopLossRate" class="form-control" width="100" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">杠杆倍数：</label>
                    <div class="col-sm-8">
                        <input name="leverage" id="laverValue" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">交易数量(张)：</label>
                    <div class="col-sm-8">
                        <input name="amount" id="inputValue" oninput="calculateResult()" width="100" type="text" required>
                        <label id="resultLabel">=  USDT</label>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">订单方向：</label>
                    <div class="col-sm-8">
                        <select name="positionSide" class="form-control" width="200">
                            <option value="LONG">做多</option>
                            <option value="SHORT">做空</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <input type="hidden" name="strategyType" value="0">
                </div>
            </div>
<!--            <div class="col-xs-12">-->
<!--                <div class="form-group">-->
<!--                    <label class="col-sm-3 control-label">状态：</label>-->
<!--                    <div class="col-sm-8">-->
<!--                        <select name="state" class="form-control" width="200">-->
<!--                            <option value=0>启用</option>-->
<!--                            <option value=-1>不启用</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
            <h4 class="form-header h4">分阶段止盈信息</h4>
            <div class="row">
                <div class="col-xs-12">
                    <button type="button" class="btn btn-white btn-sm" onclick="addRow()"><i class="fa fa-plus"> 增加</i></button>
                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delRow()"><i class="fa fa-minus"> 删除</i></button>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:src="@{/js/jquery.tmpl.js}"></script>
    <script th:inline="javascript">
        let priceMap = new Map();
        let balanceMap = new Map();
        $(document).ready(function() {
            $.ajax({
                url: "/strategy/account/getOptions",
                type: "GET",
                success: function(response) {
                    let data = response.data;
                    let exAccount = $("#exAccountId");
                    $.each(data, function(index, item) {
                        balanceMap.set(item.accountName, item.balanceFutures);
                        exAccount.append(
                            $("<option>").val(item.accountName).text(item.accountName)
                        );
                    });
                }
            });

            $.ajax({
                url: "/strategy/symbol/getOptions",
                type: "GET",
                success: function(response) {
                    let data = response.data;
                    let symbolSel = $("#symbolId");
                    $.each(data, function(index, item) {
                        priceMap.set(item.symbol, item.price);
                        symbolSel.append(
                            $("<option>").val(item.symbol).text(item.symbol)
                        );
                    });
                }
            });
        });

        function handleAccount(selectedEle) {
            let selectedValue = selectedEle.value;
            let balanceValue = balanceMap.get(selectedValue);
            let balanceSelect = document.getElementById("balanceId")
            balanceSelect.innerText = balanceValue + "  USDT";
        }

        function calculateResult() {
            let inputValue = document.getElementById("inputValue").value;
            let resultLabel = document.getElementById("resultLabel");
            let laverValue = document.getElementById("laverValue").value;
            let symbol= document.getElementById("symbolId").value;
            let curPrice = priceMap.get(symbol);

            // 这里写你的计算逻辑，例如：输入值的平方
            let result = inputValue / laverValue * curPrice;
            // 显示计算结果
            resultLabel.innerText = " = " + result + " USDT ";
        }

        var prefix = ctx + "strategy/trailing"
        $("#form-trailing-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-trailing-add').serialize());
            }
        }

        $(function() {
            var options = {
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                {
                    field: 'priceGain',
                    align: 'center',
                    title: '触发止盈/止损涨幅',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='trailingDetailList[%s].priceGain' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'takeProfit',
                    align: 'center',
                    title: '触发止盈/止损',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='trailingDetailList[%s].takeProfit' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'type',
                    align: 'center',
                    title: '止盈/止损',
                    formatter: function(value, row, index) {
                        var data = [{ index: index, type: value }];
                        return $("#trailingDetailType").tmpl(data).html();
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                        return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\')"><i class="fa fa-remove"></i>删除</a>';
                    }
                }]
            };
            $.table.init(options);
        });

        function addRow() {
            var count = $("#" + table.options.id).bootstrapTable('getData').length;
            var row = {
                index: $.table.serialNumber(count),
                setPrice: "",
                highestPrice: "",
                priceGain: "",
                takeProfit: "",
                state: "",
                type: "",
                createTime: "",
                updateTime: "",
                message: "",
            }
            sub.addRow(row);
        }
    </script>
</body>
</html>

<script id="trailingDetailType" type="text/x-jquery-tmpl">
<div>
<select class='form-control' name='trailingDetailList[${index}].type'>
    <option value="0" {{if type==="0"}}selected{{/if}}>止盈</option>
    <option value="1" {{if type==="1"}}selected{{/if}}>止损</option>
</select>
</div>
</script>