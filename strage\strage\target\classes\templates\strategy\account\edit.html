<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-account-edit" th:object="${exAccount}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">account_name：</label>
                <div class="col-sm-8">
                    <input name="accountName" th:field="*{accountName}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">apikey:</label>
                <div class="col-sm-8">
                    <input name="apikey" th:field="*{apikey}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">pool address：</label>
                <div class="col-sm-8">
                    <input name="secretKey" th:field="*{secretKey}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">pool address：</label>
                <div class="col-sm-8">
                    <input name="password" th:field="*{password}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">平台：</label>
                <div class="col-sm-8">
                    <input name="platform" th:field="*{platform}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">0: 不可用， 1：可用：</label>
                <div class="col-sm-8">
                    <input name="state" th:field="*{state}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">描述信息：</label>
                <div class="col-sm-8">
                    <input name="message" th:field="*{message}" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">描述信息：</label>
                <div class="col-sm-8">
                    <input name="secretMsg" th:field="*{secretMsg}" class="form-control" type="text">
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "strategy/account";
    $("#form-account-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-account-edit').serialize());
        }
    }
</script>
</body>
</html>