<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略交易系统</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet" />
    <link href="{{ url_for('static', filename='css/font-awesome.min.css') }}" rel="stylesheet" />
    <link href="{{ url_for('static', filename='css/style.min.css') }}" rel="stylesheet" />
    <link href="{{ url_for('static', filename='ruoyi/css/ry-ui.css') }}" rel="stylesheet" />

    <style>
        /* 标签页样式 */
        .page-heading {
            padding: 10px 20px;
            margin-bottom: 0;
        }

        .nav-tabs {
            border-bottom: 1px solid #ddd;
            margin-bottom: 0;
        }

        .nav-tabs>li>a {
            padding: 8px 15px;
            margin-right: 2px;
            border-radius: 4px 4px 0 0;
            position: relative;
        }

        .nav-tabs>li.active>a {
            background-color: #fff;
            border-color: #ddd #ddd transparent;
        }

        .close-tab {
            margin-left: 8px;
            background: none;
            border: none;
            color: #999;
            font-size: 16px;
            line-height: 1;
            opacity: 0.6;
        }

        .close-tab:hover {
            opacity: 1;
            color: #d9534f;
        }

        .tab-content {
            background-color: #fff;
            border: 1px solid #ddd;
            border-top: none;
            padding: 0;
        }

        .tab-pane {
            padding: 15px;
        }

        /* 加载动画 */
        .sk-spinner-wave {
            margin: 40px auto;
            width: 50px;
            height: 40px;
            text-align: center;
            font-size: 10px;
        }

        .sk-spinner-wave>div {
            background-color: #1ab394;
            height: 100%;
            width: 6px;
            display: inline-block;
            animation: sk-stretchdelay 1.2s infinite ease-in-out;
        }

        .sk-spinner-wave .sk-rect2 {
            animation-delay: -1.1s;
        }

        .sk-spinner-wave .sk-rect3 {
            animation-delay: -1.0s;
        }

        .sk-spinner-wave .sk-rect4 {
            animation-delay: -0.9s;
        }

        .sk-spinner-wave .sk-rect5 {
            animation-delay: -0.8s;
        }

        @keyframes sk-stretchdelay {

            0%,
            40%,
            100% {
                transform: scaleY(0.4);
            }

            20% {
                transform: scaleY(1.0);
            }
        }
    </style>
</head>

<body class="fixed-sidebar full-height-layout gray-bg">
    <div id="wrapper">
        <!-- 左侧导航 -->
        <nav class="navbar-default navbar-static-side" role="navigation">
            <div class="sidebar-collapse">
                <ul class="nav metismenu" id="side-menu">
                    <li class="nav-header">
                        <div class="dropdown profile-element">
                            <span>
                                <img alt="image" class="img-circle"
                                    src="{{ url_for('static', filename='img/profile_small.jpg') }}" />
                            </span>
                            <a data-toggle="dropdown" class="dropdown-toggle" href="#">
                                <span class="clear">
                                    <span class="block m-t-xs">
                                        <strong class="font-bold">{{ user.name if user else '用户' }}</strong>
                                    </span>
                                    <span class="text-muted text-xs block">管理员</span>
                                </span>
                            </a>
                            <ul class="dropdown-menu animated fadeInRight m-t-xs">
                                <li><a href="{{ url_for('auth.logout') }}">安全退出</a></li>
                            </ul>
                        </div>
                        <div class="logo-element">H+</div>
                    </li>

                    <!-- 策略交易菜单 -->
                    <li>
                        <a href="#"><i class="fa fa-line-chart"></i> <span class="nav-label">策略交易</span><span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            <li><a href="{{ url_for('trailing_list') }}">策略管理</a></li>
                            <li><a href="{{ url_for('account_list') }}">账户管理</a></li>
                            <li><a href="{{ url_for('symbol_list') }}">币对管理</a></li>
                        </ul>
                    </li>

                    <!-- 系统管理菜单 -->
                    <li>
                        <a href="#"><i class="fa fa-cog"></i> <span class="nav-label">系统管理</span><span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            <li><a href="#"
                                    onclick="openTab('user-manage', '用户管理', '{{ url_for('user_list') }}')">用户管理</a></li>
                        </ul>
                    </li>

                    <!-- 系统监控菜单 -->
                    <li>
                        <a href="#" onclick="openTab('operlog', '操作日志', '/monitor/operlog')"><i
                                class="fa fa-video-camera"></i> <span class="nav-label">操作日志</span></a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <div id="page-wrapper" class="gray-bg dashbard-1">
            <!-- 顶部导航 -->
            <div class="row border-bottom">
                <nav class="navbar navbar-static-top" role="navigation" style="margin-bottom: 0">
                    <div class="navbar-header">
                        <a class="navbar-minimalize minimalize-styl-2 btn btn-primary" href="#"><i
                                class="fa fa-bars"></i></a>
                    </div>
                    <ul class="nav navbar-top-links navbar-right">
                        <li>
                            <span class="m-r-sm text-muted welcome-message">欢迎使用策略交易系统</span>
                        </li>
                        <li>
                            <a href="{{ url_for('auth.logout') }}">
                                <i class="fa fa-sign-out"></i> 退出
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- 标签页导航 -->
            <div class="row border-bottom white-bg page-heading">
                <div class="col-lg-12">
                    <ul class="nav nav-tabs" id="main-tabs">
                        <li class="active">
                            <a data-toggle="tab" href="#tab-home" data-tab="home">
                                <i class="fa fa-home"></i> 首页
                                <button class="close-tab" onclick="closeTab('home')" style="display:none;">×</button>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="wrapper wrapper-content">
                <!-- 标签页内容 -->
                <div class="tab-content" id="main-tab-content">
                    <!-- 首页标签页 -->
                    <div id="tab-home" class="tab-pane active">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="ibox float-e-margins">
                                    <div class="ibox-title">
                                        <h5>系统概览</h5>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-lg-3 col-md-6">
                                                <div class="ibox float-e-margins">
                                                    <div class="ibox-title">
                                                        <span class="label label-success pull-right">运行中</span>
                                                        <h5>策略总数</h5>
                                                    </div>
                                                    <div class="ibox-content">
                                                        <h1 class="no-margins">0</h1>
                                                        <small>当前活跃策略</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="ibox float-e-margins">
                                                    <div class="ibox-title">
                                                        <span class="label label-info pull-right">今日</span>
                                                        <h5>交易次数</h5>
                                                    </div>
                                                    <div class="ibox-content">
                                                        <h1 class="no-margins">0</h1>
                                                        <small>今日执行交易</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="ibox float-e-margins">
                                                    <div class="ibox-title">
                                                        <span class="label label-primary pull-right">总计</span>
                                                        <h5>账户数量</h5>
                                                    </div>
                                                    <div class="ibox-content">
                                                        <h1 class="no-margins">0</h1>
                                                        <small>已配置账户</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-6">
                                                <div class="ibox float-e-margins">
                                                    <div class="ibox-title">
                                                        <span class="label label-warning pull-right">监控</span>
                                                        <h5>币对数量</h5>
                                                    </div>
                                                    <div class="ibox-content">
                                                        <h1 class="no-margins">0</h1>
                                                        <small>订阅币对</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="ibox float-e-margins">
                                    <div class="ibox-title">
                                        <h5>快速操作</h5>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-sm-6 col-md-3">
                                                <a href="{{ url_for('trailing_add') }}"
                                                    class="btn btn-primary btn-block">
                                                    <i class="fa fa-plus"></i> 新增策略
                                                </a>
                                            </div>
                                            <div class="col-sm-6 col-md-3">
                                                <a href="{{ url_for('account_add') }}"
                                                    class="btn btn-success btn-block">
                                                    <i class="fa fa-user-plus"></i> 添加账户
                                                </a>
                                            </div>
                                            <div class="col-sm-6 col-md-3">
                                                <a href="{{ url_for('symbol_add') }}" class="btn btn-info btn-block">
                                                    <i class="fa fa-line-chart"></i> 订阅币对
                                                </a>
                                            </div>
                                            <div class="col-sm-6 col-md-3">
                                                <a href="{{ url_for('account_asset') }}"
                                                    class="btn btn-warning btn-block">
                                                    <i class="fa fa-money"></i> 查看资产
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <!-- 首页标签页结束 -->

                    <!-- 操作日志标签页 -->
                    <div id="tab-operlog" class="tab-pane">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="ibox float-e-margins">
                                    <div class="ibox-title">
                                        <h5>操作日志</h5>
                                        <div class="ibox-tools">
                                            <a class="collapse-link">
                                                <i class="fa fa-chevron-up"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="ibox-content">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <form class="form-inline" id="operlogFormSearch">
                                                    <div class="form-group">
                                                        <label>模块标题：</label>
                                                        <input type="text" class="form-control" name="title"
                                                            placeholder="请输入模块标题">
                                                    </div>
                                                    <div class="form-group">
                                                        <label>操作人员：</label>
                                                        <input type="text" class="form-control" name="operName"
                                                            placeholder="请输入操作人员">
                                                    </div>
                                                    <div class="form-group">
                                                        <label>状态：</label>
                                                        <select name="status" class="form-control">
                                                            <option value="">所有</option>
                                                            <option value="0">正常</option>
                                                            <option value="1">异常</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group">
                                                        <button type="button" class="btn btn-primary"
                                                            onclick="searchOperLog()">
                                                            <i class="fa fa-search"></i> 搜索
                                                        </button>
                                                        <button type="button" class="btn btn-warning"
                                                            onclick="resetOperLogForm()">
                                                            <i class="fa fa-refresh"></i> 重置
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-sm-12 select-table table-striped">
                                                <table id="operlog-table"
                                                    class="table table-striped table-bordered table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>日志编号</th>
                                                            <th>系统模块</th>
                                                            <th>操作类型</th>
                                                            <th>操作人员</th>
                                                            <th>主机地址</th>
                                                            <th>操作状态</th>
                                                            <th>操作时间</th>
                                                            <th>消耗时间</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="operlog-tbody">
                                                        <!-- 数据将通过Ajax加载 -->
                                                    </tbody>
                                                </table>

                                                <!-- 分页 -->
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <div class="dataTables_info" id="operlog-info">
                                                            显示第 1 到 10 项，共 0 项
                                                        </div>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <div class="dataTables_paginate paging_simple_numbers"
                                                            id="operlog-pagination">
                                                            <!-- 分页按钮将通过JavaScript生成 -->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 操作日志标签页结束 -->

                </div>
                <!-- 标签页内容结束 -->
            </div>
        </div>
    </div>

    <!-- 全局js -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/plugins/metisMenu/jquery.metisMenu.js') }}"></script>
    <script src="{{ url_for('static', filename='js/plugins/slimscroll/jquery.slimscroll.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/hplus.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/plugins/pace/pace.min.js') }}"></script>

    <script>
        // 标签页管理
        var openTabs = ['home']; // 已打开的标签页
        var currentTab = 'home'; // 当前活跃标签页

        // 操作日志相关变量
        var currentPage = 1;
        var pageSize = 10;
        var totalRecords = 0;

        $(document).ready(function () {
            // 初始化菜单
            $('#side-menu').metisMenu();

            // 最小化侧边栏
            $('.navbar-minimalize').click(function () {
                $("body").toggleClass("mini-navbar");
                SmoothlyMenu();
            });

            // 初始化标签页
            initTabs();
        });

        function SmoothlyMenu() {
            if (!$('body').hasClass('mini-navbar') || $('body').hasClass('body-small')) {
                $('#side-menu').hide();
                setTimeout(function () {
                    $('#side-menu').fadeIn(400);
                }, 200);
            } else if ($('body').hasClass('fixed-sidebar')) {
                $('#side-menu').hide();
                setTimeout(function () {
                    $('#side-menu').fadeIn(400);
                }, 100);
            } else {
                $('#side-menu').removeAttr('style');
            }
        }

        // 初始化标签页
        function initTabs() {
            // 绑定标签页切换事件
            $('#main-tabs a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var tabId = $(e.target).data('tab');
                currentTab = tabId;

                // 如果是操作日志标签页，加载数据
                if (tabId === 'operlog') {
                    loadOperLogData();
                }
            });
        }

        // 打开新标签页
        function openTab(tabId, title, url) {
            // 检查标签页是否已存在
            if (openTabs.indexOf(tabId) !== -1) {
                // 标签页已存在，直接激活
                activateTab(tabId);
                return;
            }

            // 创建新标签页
            createTab(tabId, title, url);

            // 添加到已打开列表
            openTabs.push(tabId);

            // 激活新标签页
            activateTab(tabId);

            // 如果是操作日志，加载数据
            if (tabId === 'operlog') {
                setTimeout(function () {
                    loadOperLogData();
                }, 100);
            }
        }

        // 创建标签页
        function createTab(tabId, title, url) {
            // 创建标签页导航
            var tabNav = '<li>' +
                '<a data-toggle="tab" href="#tab-' + tabId + '" data-tab="' + tabId + '">' +
                '<i class="fa fa-file-text-o"></i> ' + title +
                '<button class="close-tab" onclick="closeTab(\'' + tabId + '\')" style="margin-left: 8px; background: none; border: none; color: #999;">×</button>' +
                '</a>' +
                '</li>';

            $('#main-tabs').append(tabNav);

            // 如果是操作日志标签页，显示已有内容
            if (tabId === 'operlog') {
                // 操作日志标签页内容已经存在，只需要显示
                return;
            }

            // 为其他标签页创建内容区域
            var tabContent = '<div id="tab-' + tabId + '" class="tab-pane">' +
                '<div class="sk-spinner sk-spinner-wave">' +
                '<div class="sk-rect1"></div>' +
                '<div class="sk-rect2"></div>' +
                '<div class="sk-rect3"></div>' +
                '<div class="sk-rect4"></div>' +
                '<div class="sk-rect5"></div>' +
                '</div>' +
                '</div>';

            $('#main-tab-content').append(tabContent);

            // 加载页面内容
            if (url) {
                loadTabContent(tabId, url);
            }
        }

        // 激活标签页
        function activateTab(tabId) {
            // 移除所有活跃状态
            $('#main-tabs li').removeClass('active');
            $('.tab-pane').removeClass('active');

            // 激活指定标签页
            $('#main-tabs a[data-tab="' + tabId + '"]').parent().addClass('active');
            $('#tab-' + tabId).addClass('active');

            currentTab = tabId;
        }

        // 关闭标签页
        function closeTab(tabId) {
            // 首页不能关闭
            if (tabId === 'home') {
                return;
            }

            // 移除标签页
            $('#main-tabs a[data-tab="' + tabId + '"]').parent().remove();
            $('#tab-' + tabId).remove();

            // 从已打开列表中移除
            var index = openTabs.indexOf(tabId);
            if (index > -1) {
                openTabs.splice(index, 1);
            }

            // 如果关闭的是当前标签页，激活首页
            if (currentTab === tabId) {
                activateTab('home');
            }
        }

        // 加载标签页内容
        function loadTabContent(tabId, url) {
            $.get(url, function (data) {
                $('#tab-' + tabId).html(data);
            }).fail(function () {
                $('#tab-' + tabId).html('<div class="alert alert-danger">加载失败，请稍后重试</div>');
            });
        }

        // 加载操作日志数据
        function loadOperLogData() {
            var formData = $('#operlogFormSearch').serialize();
            formData += '&pageNum=' + currentPage + '&pageSize=' + pageSize;

            $.ajax({
                type: 'POST',
                url: '/api/monitor/operlog/list',
                data: formData,
                success: function (response) {
                    if (response.code === 0) {
                        renderOperLogTable(response.rows);
                        renderOperLogPagination(response.total);
                        updateOperLogInfo(response.total);
                    } else {
                        alert('加载操作日志失败: ' + response.msg);
                    }
                },
                error: function () {
                    alert('请求失败，请稍后重试');
                }
            });
        }

        // 渲染操作日志表格
        function renderOperLogTable(data) {
            var tbody = $('#operlog-tbody');
            tbody.empty();

            if (data.length === 0) {
                tbody.append('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
                return;
            }

            data.forEach(function (item) {
                var businessTypeLabel = getBusinessTypeLabel(item.business_type);
                var statusLabel = getStatusLabel(item.status);
                var operTime = formatDateTime(item.oper_time);
                var costTime = item.cost_time + ' 毫秒';

                var row = '<tr>' +
                    '<td>' + (item.oper_id || '') + '</td>' +
                    '<td>' + (item.title || '') + '</td>' +
                    '<td>' + businessTypeLabel + '</td>' +
                    '<td>' + (item.oper_name || '') + '</td>' +
                    '<td>' + (item.oper_ip || '') + '</td>' +
                    '<td>' + statusLabel + '</td>' +
                    '<td>' + operTime + '</td>' +
                    '<td>' + costTime + '</td>' +
                    '</tr>';
                tbody.append(row);
            });
        }

        // 获取业务类型标签
        function getBusinessTypeLabel(type) {
            switch (type) {
                case 0: return '<span class="label label-info">其它</span>';
                case 1: return '<span class="label label-success">新增</span>';
                case 2: return '<span class="label label-warning">修改</span>';
                case 3: return '<span class="label label-danger">删除</span>';
                default: return '<span class="label label-default">未知</span>';
            }
        }

        // 获取状态标签
        function getStatusLabel(status) {
            if (status == 0) {
                return '<span class="label label-success">正常</span>';
            } else {
                return '<span class="label label-danger">异常</span>';
            }
        }

        // 格式化日期时间
        function formatDateTime(dateTime) {
            if (!dateTime) return '';
            var date = new Date(dateTime);
            return date.getFullYear() + '-' +
                String(date.getMonth() + 1).padStart(2, '0') + '-' +
                String(date.getDate()).padStart(2, '0') + ' ' +
                String(date.getHours()).padStart(2, '0') + ':' +
                String(date.getMinutes()).padStart(2, '0') + ':' +
                String(date.getSeconds()).padStart(2, '0');
        }

        // 渲染分页
        function renderOperLogPagination(total) {
            totalRecords = total;
            var totalPages = Math.ceil(total / pageSize);
            var pagination = $('#operlog-pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            var paginationHtml = '<ul class="pagination">';

            // 上一页
            if (currentPage > 1) {
                paginationHtml += '<li><a href="#" onclick="gotoOperLogPage(' + (currentPage - 1) + ')">上一页</a></li>';
            }

            // 页码
            var startPage = Math.max(1, currentPage - 2);
            var endPage = Math.min(totalPages, currentPage + 2);

            for (var i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    paginationHtml += '<li class="active"><a href="#">' + i + '</a></li>';
                } else {
                    paginationHtml += '<li><a href="#" onclick="gotoOperLogPage(' + i + ')">' + i + '</a></li>';
                }
            }

            // 下一页
            if (currentPage < totalPages) {
                paginationHtml += '<li><a href="#" onclick="gotoOperLogPage(' + (currentPage + 1) + ')">下一页</a></li>';
            }

            paginationHtml += '</ul>';
            pagination.html(paginationHtml);
        }

        // 跳转到指定页
        function gotoOperLogPage(page) {
            currentPage = page;
            loadOperLogData();
        }

        // 更新信息显示
        function updateOperLogInfo(total) {
            var start = (currentPage - 1) * pageSize + 1;
            var end = Math.min(currentPage * pageSize, total);
            $('#operlog-info').text('显示第 ' + start + ' 到 ' + end + ' 项，共 ' + total + ' 项');
        }

        // 搜索操作日志
        function searchOperLog() {
            currentPage = 1;
            loadOperLogData();
        }

        // 重置搜索表单
        function resetOperLogForm() {
            $('#operlogFormSearch')[0].reset();
            currentPage = 1;
            loadOperLogData();
        }
    </script>

</body>

</html>