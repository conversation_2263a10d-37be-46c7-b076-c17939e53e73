<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('跟踪止盈列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>用户名称：</label>
                                <input type="text" name="userName"/>
                            </li>
                            <li>
                                <label>平台：</label>
                                <input type="text" name="platform"/>
                            </li>
                            <li>
                                <label>币名：</label>
                                <input type="text" name="symbol"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <input type="text" name="state"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="strategy:trailing:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.addGroup()" shiro:hasPermission="strategy:trailing:remove">
                    <i class="fa fa-plus"></i> 创建策略组
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.startGroup()" shiro:hasPermission="strategy:trailing:remove">
                    <i class="fa fa-plus"></i> 启动策略组
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.stopGroup()" shiro:hasPermission="strategy:trailing:remove">
                    <i class="fa fa-plus"></i> 停止策略组
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.follow(id)" shiro:hasPermission="strategy:trailing:remove">
                    <i class="fa fa-plus"></i> 配置跟单策略
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-auto-refresh="true" data-auto-refresh-interval="60"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
     <th:block th:include="include :: bootstrap-table-auto-refresh-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('strategy:trailing:edit')}]];
        var removeFlag = [[${@permission.hasPermi('strategy:trailing:remove')}]];
        var prefix = ctx + "strategy/trailing";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                addGroup: prefix + "/addGroup",
                startGroup: prefix + "/startGroup",
                stopGroup: prefix + "/stopGroup",
                follow: prefix + "/follow/{id}",
                modalName: "跟踪止盈",
                columns: [
                    {
                        checkbox: true
                    },
                    {
                        field: 'id',
                        title: '${comment}',
                        visible: false
                    },
                    {
                        field: 'groupId',
                        title: '策略组'
                    },
                {
                    field: 'accountName',
                    title: 'api名称'
                },
                {
                    field: 'platform',
                    title: '平台'
                },
                {
                    field: 'symbol',
                    title: '币名'
                },
                {
                    field: 'openPrice',
                    title: '真实建仓价'
                },
                {
                    field: 'marketPrice',
                    title: '最新价'
                },
                {
                    field: 'stopLossPrice',
                    title: '止损价'
                },
                {
                    field: 'amount',
                    title: '交易数量'
                },
                {
                    field: 'profitValue',
                    title: '收益(U)'
                },
                {
                    field: 'profitRate',
                    title: '收益率%'
                },
                {
                    field: 'leverage',
                    title: '杠杆'
                },
                {
                    field: 'positionSide',
                    title: '订单方向',
                    formatter: function(value, row, index) {
                        var display = '做空';
                        if(value === 'LONG') {
                            display = '做多';
                        } else if(value === 'SHORT') {
                            display = '做空';
                        } else {
                            display = '--';
                        }
                        return display;
                    }
                },
                {
                    field: 'state',
                    title: '状态',
                    formatter: function(value, row, index) {
                        var display = '';
                        if(value == -1) {
                            display = '不启用';
                        } else if(value == 0) {
                            display = '待处理';
                        } else if(value == 1) {
                            display = '持仓中';
                        } else if(value == 4) {
                            display = '已止盈';
                        } else if(value == 5) {
                            display = '已止损';
                        } else if(value == 6) {
                            display = '手动停止';
                        } else if(value == 7) {
                            display = '跟单中';
                        } else {
                            display = value;
                        }

                        return display;
                    }
                },
                    {
                        field: 'strategyType',
                        title: '策略类型',
                        formatter: function(value, row, index) {
                            var display = '普通策略';
                            if(value == 0) {
                                display = '普通策略';
                            } else if( value == 1) {
                                display = '跟单策略';
                            }
                            return display;
                        }
                    },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>