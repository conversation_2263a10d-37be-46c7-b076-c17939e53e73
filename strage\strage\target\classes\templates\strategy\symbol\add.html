<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增【请填写功能名称】')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-symbol-add">
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">平台：</label>
                <div class="col-sm-8">
                    <select name="platform" class="form-control" width="200">
                        <option value="BINANCE">币安</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">币对名：</label>
                <div class="col-sm-8">
                    <input name="symbol" class="form-control" type="text">
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <select name="state" class="form-control" width="200">
                        <option value=1>启用</option>
                        <option value=0>不启用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-xs-12">
            <div class="form-group">
                <label class="col-sm-3 control-label">描述信息：</label>
                <div class="col-sm-8">
                    <input name="message" class="form-control" type="text">
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "strategy/symbol"
    $("#form-symbol-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-symbol-add').serialize());
        }
    }
</script>
</body>
</html>